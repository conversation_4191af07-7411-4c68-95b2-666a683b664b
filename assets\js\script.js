// PWA Service Worker Registration
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
            .then(registration => {
                console.log('SW registered: ', registration);

                // Check for updates
                registration.addEventListener('updatefound', () => {
                    const newWorker = registration.installing;
                    newWorker.addEventListener('statechange', () => {
                        if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                            // New content is available, prompt user to refresh
                            if (confirm('גרסה חדשה זמינה. האם ברצונך לרענן את הדף?')) {
                                window.location.reload();
                            }
                        }
                    });
                });
            })
            .catch(error => {
                console.log('SW registration failed: ', error);
            });
    });
}

// PWA Install Prompt
let deferredPrompt;
window.addEventListener('beforeinstallprompt', (e) => {
    console.log('PWA install prompt triggered');
    e.preventDefault();
    deferredPrompt = e;

    // Show install button if needed
    const installButton = document.getElementById('install-button');
    if (installButton) {
        installButton.style.display = 'block';
        installButton.addEventListener('click', () => {
            deferredPrompt.prompt();
            deferredPrompt.userChoice.then((choiceResult) => {
                if (choiceResult.outcome === 'accepted') {
                    console.log('User accepted the PWA install prompt');
                } else {
                    console.log('User dismissed the PWA install prompt');
                }
                deferredPrompt = null;
            });
        });
    }
});

// Mobile Navigation Toggle
document.addEventListener('DOMContentLoaded', function() {
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');
    
    hamburger.addEventListener('click', function() {
        hamburger.classList.toggle('active');
        navMenu.classList.toggle('active');
    });
    
    // Close menu when clicking on a link
    document.querySelectorAll('.nav-link').forEach(link => {
        link.addEventListener('click', () => {
            hamburger.classList.remove('active');
            navMenu.classList.remove('active');
        });
    });
    
    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Header scroll effect
    window.addEventListener('scroll', function() {
        const header = document.querySelector('.header');
        if (window.scrollY > 100) {
            header.style.background = 'rgba(18, 18, 18, 0.98)';
        } else {
            header.style.background = 'rgba(18, 18, 18, 0.95)';
        }
    });
    
    // Set minimum datetime for booking
    const datetimeInput = document.getElementById('datetime');
    if (datetimeInput) {
        const now = new Date();
        now.setMinutes(now.getMinutes() - now.getTimezoneOffset());
        datetimeInput.min = now.toISOString().slice(0, 16);
    }
    
    // Add scroll animations
    addScrollAnimations();

    // Remove any floating WhatsApp buttons
    removeFloatingWhatsApp();
});

// WhatsApp phone number - Taxi Owner contact
const phoneNumber = '972549312311';

// Contact form submission
const contactForm = document.getElementById('contactForm');
if (contactForm) {
    contactForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Get form data
        const formData = new FormData(this);
        const name = formData.get('name');
        const phone = formData.get('phone');
        const from = formData.get('from');
        const to = formData.get('to');
        const datetime = formData.get('datetime');
        const payment = formData.get('payment');
        const passengers = formData.get('passengers');
        const message = formData.get('message');
        
        // Validate required fields
        if (!name || !phone || !from || !to || !datetime || !payment || !passengers) {
            showNotification('אנא מלא את כל השדות הנדרשים', 'error');
            return;
        }
        
        // Format datetime
        const dateObj = new Date(datetime);
        const formattedDate = dateObj.toLocaleDateString('he-IL');
        const formattedTime = dateObj.toLocaleTimeString('he-IL', { 
            hour: '2-digit', 
            minute: '2-digit' 
        });
        
        // Create WhatsApp message
        let whatsappMessage = `🚖 *הזמנת נסיעה - מונית אמיר*\n\n`;
        whatsappMessage += `שלום, אני מעוניין להזמין נסיעה:\n\n`;
        whatsappMessage += `👤 *שם:* ${name}\n`;
        whatsappMessage += `📱 *טלפון:* ${phone}\n`;
        whatsappMessage += `📍 *מנקודת איסוף:* ${from}\n`;
        whatsappMessage += `🎯 *ליעד:* ${to}\n`;
        whatsappMessage += `📅 *תאריך:* ${formattedDate}\n`;
        whatsappMessage += `🕐 *שעה:* ${formattedTime}\n`;
        whatsappMessage += `👥 *מספר נוסעים:* ${passengers}\n`;
        whatsappMessage += `💳 *אמצעי תשלום:* ${payment}\n`;

        if (message) {
            whatsappMessage += `💬 *הערות נוספות:* ${message}\n`;
        }

        whatsappMessage += `\n✅ *הזמנה דרך האתר:* https://monit-amir.web.app\n`;
        whatsappMessage += `\nתודה רבה! 🙏`;
        
        // Encode message for URL
        const encodedMessage = encodeURIComponent(whatsappMessage);
        
        // Create WhatsApp URL
        const whatsappURL = `https://wa.me/${phoneNumber}?text=${encodedMessage}`;
        
        // Show success message
        showNotification('🚖 הזמנתך נשלחה בהצלחה! מעביר אותך לוואטסאפ של מונית אמיר...', 'success');
        
        // Open WhatsApp after a short delay
        setTimeout(() => {
            window.open(whatsappURL, '_blank');
        }, 1500);
        
        // Reset form
        this.reset();
    });
}

// Notification system
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => notification.remove());
    
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
            <span>${message}</span>
        </div>
    `;
    
    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 10px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        max-width: 300px;
        font-family: 'Heebo', Arial, sans-serif;
        direction: rtl;
    `;
    
    notification.querySelector('.notification-content').style.cssText = `
        display: flex;
        align-items: center;
        gap: 0.5rem;
    `;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Auto remove after 4 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 4000);
}

// Remove floating WhatsApp buttons
function removeFloatingWhatsApp() {
    // Remove any existing floating WhatsApp elements
    const selectors = [
        '.floating-whatsapp',
        '.whatsapp-float',
        '.wa-float',
        '.floating-wa',
        '[class*="floating"][class*="whatsapp"]',
        '[class*="whatsapp"][class*="float"]'
    ];

    selectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(el => el.remove());
    });

    // Also check for elements with inline styles that might be floating WhatsApp
    const allDivs = document.querySelectorAll('div');
    allDivs.forEach(div => {
        const style = div.style.cssText || '';
        if (style.includes('position: fixed') && style.includes('bottom') &&
            (div.innerHTML.includes('whatsapp') || div.innerHTML.includes('wa.me'))) {
            div.remove();
        }
    });

    // Set up observer to remove any dynamically added WhatsApp buttons
    const observer = new MutationObserver(mutations => {
        mutations.forEach(mutation => {
            mutation.addedNodes.forEach(node => {
                if (node.nodeType === 1) { // Element node
                    const element = node;
                    if (element.className && (
                        element.className.includes('floating') && element.className.includes('whatsapp') ||
                        element.className.includes('whatsapp') && element.className.includes('float')
                    )) {
                        element.remove();
                    }

                    // Check for WhatsApp links in fixed positioned elements
                    if (element.style && element.style.position === 'fixed' &&
                        element.innerHTML && element.innerHTML.includes('wa.me')) {
                        element.remove();
                    }
                }
            });
        });
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
}



// Scroll animations
function addScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    // Observe elements for animation
    const animatedElements = document.querySelectorAll('.service-card, .about-text, .about-image, .contact-form');
    animatedElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    removeFloatingWhatsApp();
    addScrollAnimations();
});
