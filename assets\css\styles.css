/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: '<PERSON><PERSON><PERSON>', <PERSON><PERSON>, sans-serif;
    direction: rtl;
    background-color: #121212;
    color: #ffffff;
    line-height: 1.6;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(18, 18, 18, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    transition: all 0.3s ease;
}

.navbar {
    padding: 1rem 0;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.nav-logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: #FFD700;
    text-decoration: none;
    transition: color 0.3s ease;
}

.nav-logo:hover {
    color: #FFA500;
}

.nav-logo i {
    margin-left: 10px;
    font-size: 1.8rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    color: #ffffff;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: #FFD700;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    right: 0;
    width: 0;
    height: 2px;
    background: #FFD700;
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: #FFD700;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    display: flex;
    align-items: center;
    padding-top: 80px;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-title {
    font-size: 4rem;
    font-weight: 700;
    color: #FFD700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.hero-subtitle {
    font-size: 1.3rem;
    color: #cccccc;
    margin-bottom: 2rem;
    line-height: 1.8;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border: none;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    cursor: pointer;
    text-align: center;
}

.btn-primary {
    background: linear-gradient(45deg, #FFD700, #FFC107);
    color: #000000;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
}

.btn-secondary {
    background: transparent;
    color: #FFD700;
    border: 2px solid #FFD700;
}

.btn-secondary:hover {
    background: #FFD700;
    color: #000000;
    transform: translateY(-2px);
}

.hero-image {
    flex: 1;
    text-align: center;
    position: relative;
}

.image-container {
    position: relative;
    display: inline-block;
    width: 100%;
    max-width: 500px;
    height: 400px;
}

.image-circle {
    position: absolute;
    width: 250px;
    height: 250px;
    border-radius: 50%;
    overflow: hidden;
    box-shadow: 0 15px 35px rgba(255, 215, 0, 0.4);
    border: 4px solid #FFD700;
    transition: all 0.3s ease;
    animation: float 6s ease-in-out infinite;
}

.image-circle:hover {
    transform: scale(1.05);
    box-shadow: 0 20px 40px rgba(255, 215, 0, 0.6);
}

.image-circle-1 {
    top: 0;
    left: 0;
    z-index: 2;
    animation-delay: 0s;
}

.image-circle-2 {
    top: 80px;
    right: 0;
    z-index: 1;
    animation-delay: 3s;
}

.image-circle img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.image-circle:hover img {
    transform: scale(1.1);
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* Services Section */
.services {
    padding: 5rem 0;
    background: #1a1a1a;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    color: #FFD700;
    margin-bottom: 3rem;
    font-weight: 700;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.service-card {
    background: #2d2d2d;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid #333;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(255, 215, 0, 0.2);
    border-color: #FFD700;
}

.service-icon {
    font-size: 3rem;
    color: #FFD700;
    margin-bottom: 1rem;
}

.service-card h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #ffffff;
}

.service-card p {
    color: #cccccc;
    line-height: 1.6;
}

/* About Section */
.about {
    padding: 5rem 0;
    background: #121212;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text h2 {
    text-align: right;
    margin-bottom: 2rem;
}

.about-text p {
    font-size: 1.1rem;
    color: #cccccc;
    margin-bottom: 2rem;
    line-height: 1.8;
}

.features {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.feature {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: #cccccc;
}

.feature i {
    color: #FFD700;
    font-size: 1.2rem;
}

.about-image img {
    max-width: 100%;
    height: auto;
    border-radius: 15px;
    box-shadow: 0 15px 30px rgba(0,0,0,0.3);
}

/* Contact Section */
.contact {
    padding: 5rem 0;
    background: #1a1a1a;
}

.contact-content {
    display: flex;
    justify-content: center;
    margin-top: 3rem;
}

.contact-form {
    max-width: 600px;
    width: 100%;
}

.contact-form h3 {
    color: #FFD700;
    margin-bottom: 2rem;
    font-size: 1.5rem;
    text-align: center;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #FFD700;
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    border: 2px solid #333;
    border-radius: 10px;
    background: #2d2d2d;
    color: #ffffff;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #FFD700;
}

.form-group select {
    cursor: pointer;
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

/* Footer */
.footer {
    background: #0f0f0f;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    color: #FFD700;
    margin-bottom: 1rem;
}

.footer-section p,
.footer-section li {
    color: #cccccc;
    margin-bottom: 0.5rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section a {
    color: #cccccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: #FFD700;
}

/* Footer phone link styling */
.footer-section p a {
    color: #FFD700;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.footer-section p a:hover {
    color: #FFA500;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #333;
    color: #888;
}

.footer-bottom a {
    color: #FFD700;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.footer-bottom a:hover {
    color: #FFA500;
}

/* Developer Contact Icons */
.developer-contact-icons {
    display: flex;
    align-items: center;
    margin-top: 10px;
    justify-content: flex-start;
}

.contact-icon {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    text-decoration: none;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    font-size: 18px;
    margin: 0 15px;
}

.contact-icon:not(:last-child)::after {
    content: '|';
    position: absolute;
    right: -15px;
    top: 50%;
    transform: translateY(-50%);
    color: #888;
    font-size: 24px;
    font-weight: 300;
    line-height: 1;
}

.contact-icon:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.phone-icon {
    background-color: #007bff;
    color: white;
}

.phone-icon:hover {
    background-color: #0056b3;
    color: white;
}

.whatsapp-icon {
    background-color: #25D366;
    color: white;
}

.whatsapp-icon:hover {
    background-color: #128C7E;
    color: white;
}

.email-icon {
    background-color: #dc3545;
    color: white;
}

.email-icon:hover {
    background-color: #c82333;
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }
    
    .nav-menu {
        position: fixed;
        right: -100%;
        top: 70px;
        flex-direction: column;
        background: rgba(18, 18, 18, 0.95);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        padding: 2rem 0;
    }
    
    .nav-menu.active {
        right: 0;
    }
    
    .hero-content,
    .about-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-buttons {
        justify-content: center;
    }

    /* Mobile responsive for hero images */
    .image-container {
        height: 300px;
        max-width: 350px;
    }

    .image-circle {
        width: 180px;
        height: 180px;
    }

    .image-circle-1 {
        top: 10px;
        left: 10px;
    }

    .image-circle-2 {
        top: 60px;
        right: 10px;
    }
    
    .services-grid {
        grid-template-columns: 1fr;
    }

    .page-title {
        font-size: 2.5rem;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .about-text {
        order: 2;
    }

    .about-image {
        order: 1;
    }

    .contact-content {
        flex-direction: column;
    }

    .form-row {
        flex-direction: column;
        gap: 0;
    }

    .contact-form {
        padding: 30px 20px;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .cta-buttons .btn {
        width: 100%;
        max-width: 300px;
    }
}

/* Page Header */
.page-header {
    padding: 120px 0 80px;
    background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
    text-align: center;
}

.page-title {
    font-size: 3rem;
    color: #FFD700;
    margin-bottom: 20px;
    font-weight: 700;
}

.page-subtitle {
    font-size: 1.2rem;
    color: #ccc;
    max-width: 600px;
    margin: 0 auto;
}

/* Services Grid for separate page */
.services .services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.services .service-card {
    background: #2a2a2a;
    padding: 40px 30px;
    border-radius: 15px;
    border: 1px solid #333;
    text-align: center;
    transition: all 0.3s ease;
}

.services .service-card:hover {
    transform: translateY(-10px);
    border-color: #FFD700;
    box-shadow: 0 20px 40px rgba(255, 215, 0, 0.1);
}

.services .service-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #FFD700, #FFA500);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.services .service-icon i {
    font-size: 2rem;
    color: #1a1a1a;
}

.services .service-card h3 {
    color: #FFD700;
    margin-bottom: 15px;
    font-size: 1.5rem;
}

.services .service-card p {
    color: #ccc;
    margin-bottom: 20px;
    line-height: 1.6;
}

.service-features {
    list-style: none;
    padding: 0;
    text-align: right;
}

.service-features li {
    color: #fff;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.service-features i {
    color: #FFD700;
    margin-left: 10px;
    font-size: 0.9rem;
}

/* About Section for separate page */
.about .about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.about .about-text h2 {
    color: #FFD700;
    margin-bottom: 30px;
    font-size: 2.5rem;
}

.about .about-text p {
    color: #ccc;
    line-height: 1.8;
    margin-bottom: 20px;
    font-size: 1.1rem;
}

.about .features {
    margin-top: 30px;
}

.about .feature {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-bottom: 15px;
    color: #fff;
}

.about .feature i {
    color: #FFD700;
    margin-left: 15px;
    font-size: 1.1rem;
}

.about .about-image img {
    width: 100%;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Values Section */
.values {
    padding: 80px 0;
    background: #2a2a2a;
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.value-card {
    background: #1a1a1a;
    padding: 40px 30px;
    border-radius: 15px;
    text-align: center;
    border: 1px solid #333;
    transition: all 0.3s ease;
}

.value-card:hover {
    transform: translateY(-5px);
    border-color: #FFD700;
}

.value-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #FFD700, #FFA500);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.value-icon i {
    font-size: 1.8rem;
    color: #1a1a1a;
}

.value-card h3 {
    color: #FFD700;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.value-card p {
    color: #ccc;
    line-height: 1.6;
}

/* Stats Section */
.stats {
    padding: 80px 0;
    background: #1a1a1a;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 40px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    color: #FFD700;
    margin-bottom: 10px;
}

.stat-label {
    color: #ccc;
    font-size: 1.1rem;
}

/* Contact Methods */
.contact-methods {
    padding: 80px 0;
    background: #2a2a2a;
}

.contact-methods-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.contact-method {
    background: #1a1a1a;
    padding: 40px 30px;
    border-radius: 15px;
    text-align: center;
    border: 1px solid #333;
    transition: all 0.3s ease;
}

.contact-method:hover {
    transform: translateY(-5px);
    border-color: #FFD700;
}

.method-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #FFD700, #FFA500);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.method-icon i {
    font-size: 1.8rem;
    color: #1a1a1a;
}

.contact-method h3 {
    color: #FFD700;
    margin-bottom: 10px;
    font-size: 1.3rem;
}

.contact-method p {
    color: #ccc;
    margin-bottom: 15px;
}

.method-link {
    color: #FFD700;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.method-link:hover {
    color: #FFA500;
}

/* Contact Form Updates */
.contact .contact-content {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    gap: 40px;
}

.contact .contact-form {
    flex: 1;
    max-width: 600px;
    background: #2a2a2a;
    padding: 40px;
    border-radius: 15px;
    border: 1px solid #333;
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.form-row .form-group {
    flex: 1;
}

.btn-large {
    width: 100%;
    padding: 15px 30px;
    font-size: 18px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Contact Info Section */
.contact-info-section {
    padding: 80px 0;
    background: #2a2a2a;
}

.contact-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.info-card {
    background: #1a1a1a;
    padding: 40px 30px;
    border-radius: 15px;
    text-align: center;
    border: 1px solid #333;
}

.info-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #FFD700, #FFA500);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.info-icon i {
    font-size: 1.8rem;
    color: #1a1a1a;
}

.info-card h3 {
    color: #FFD700;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.info-card p {
    color: #ccc;
    line-height: 1.6;
}

/* CTA Section */
.cta {
    padding: 80px 0;
    background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
    text-align: center;
}

.cta-content h2 {
    color: #FFD700;
    margin-bottom: 20px;
    font-size: 2.5rem;
}

.cta-content p {
    color: #ccc;
    margin-bottom: 40px;
    font-size: 1.2rem;
}

.cta-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Active Navigation Link */
.nav-link.active {
    color: #FFD700;
}

.nav-link.active::after {
    width: 100%;
}
