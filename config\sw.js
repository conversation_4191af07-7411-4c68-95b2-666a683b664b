// Service Worker for Monit Amir Taxi Israel PWA
// מונית אמיר - Service Worker

const CACHE_NAME = 'monit-amir-v1.0.0';
const urlsToCache = [
  '/',
  '/index.html',
  '/services.html',
  '/contact.html',
  '/about.html',
  '/404.html',
  '/assets/css/styles.css',
  '/assets/js/script.js',
  '/assets/images/taxi-main.jpg',
  '/assets/images/taxi-about.jpg',
  '/manifest.json',
  '/schema.json',
  '/local-business.json',
  'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'
];

// Install event - cache resources
self.addEventListener('install', event => {
  console.log('Service Worker: Installing...');
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        console.log('Service Worker: Caching files');
        return cache.addAll(urlsToCache);
      })
      .then(() => {
        console.log('Service Worker: Cached all files successfully');
        return self.skipWaiting();
      })
      .catch(err => {
        console.log('Service Worker: Cache failed', err);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
  console.log('Service Worker: Activating...');
  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cacheName => {
          if (cacheName !== CACHE_NAME) {
            console.log('Service Worker: Deleting old cache', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    }).then(() => {
      console.log('Service Worker: Activated');
      return self.clients.claim();
    })
  );
});

// Fetch event - serve cached content when offline
self.addEventListener('fetch', event => {
  // Skip non-GET requests
  if (event.request.method !== 'GET') {
    return;
  }

  // Skip external requests (except Font Awesome)
  if (!event.request.url.startsWith(self.location.origin) && 
      !event.request.url.includes('cdnjs.cloudflare.com')) {
    return;
  }

  event.respondWith(
    caches.match(event.request)
      .then(response => {
        // Return cached version or fetch from network
        if (response) {
          console.log('Service Worker: Serving from cache', event.request.url);
          return response;
        }

        console.log('Service Worker: Fetching from network', event.request.url);
        return fetch(event.request).then(response => {
          // Check if we received a valid response
          if (!response || response.status !== 200 || response.type !== 'basic') {
            return response;
          }

          // Clone the response
          const responseToCache = response.clone();

          // Add to cache
          caches.open(CACHE_NAME)
            .then(cache => {
              cache.put(event.request, responseToCache);
            });

          return response;
        }).catch(() => {
          // If both cache and network fail, show offline page
          if (event.request.destination === 'document') {
            return caches.match('/404.html');
          }
        });
      })
  );
});

// Background sync for contact forms
self.addEventListener('sync', event => {
  if (event.tag === 'contact-form-sync') {
    console.log('Service Worker: Background sync for contact form');
    event.waitUntil(syncContactForm());
  }
});

// Push notification handler
self.addEventListener('push', event => {
  console.log('Service Worker: Push notification received');
  
  const options = {
    body: event.data ? event.data.text() : 'מונית אמיר - הודעה חדשה',
    icon: '/assets/images/taxi-icon-192.png',
    badge: '/assets/images/taxi-icon-72.png',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    },
    actions: [
      {
        action: 'call',
        title: 'התקשר עכשיו',
        icon: '/assets/images/phone-icon.png'
      },
      {
        action: 'close',
        title: 'סגור',
        icon: '/assets/images/close-icon.png'
      }
    ]
  };

  event.waitUntil(
    self.registration.showNotification('מונית אמיר', options)
  );
});

// Notification click handler
self.addEventListener('notificationclick', event => {
  console.log('Service Worker: Notification clicked');
  
  event.notification.close();

  if (event.action === 'call') {
    // Open phone dialer
    event.waitUntil(
      clients.openWindow('tel:+972549312311')
    );
  } else if (event.action === 'close') {
    // Just close the notification
    return;
  } else {
    // Default action - open the app
    event.waitUntil(
      clients.matchAll().then(clientList => {
        for (const client of clientList) {
          if (client.url === '/' && 'focus' in client) {
            return client.focus();
          }
        }
        if (clients.openWindow) {
          return clients.openWindow('/');
        }
      })
    );
  }
});

// Helper function to sync contact form data
async function syncContactForm() {
  try {
    // Get stored form data from IndexedDB or localStorage
    const formData = localStorage.getItem('pendingContactForm');
    if (formData) {
      const data = JSON.parse(formData);
      
      // Send to server (replace with actual endpoint)
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
      });

      if (response.ok) {
        // Remove from storage after successful sync
        localStorage.removeItem('pendingContactForm');
        console.log('Service Worker: Contact form synced successfully');
      }
    }
  } catch (error) {
    console.log('Service Worker: Contact form sync failed', error);
  }
}

// Message handler for communication with main thread
self.addEventListener('message', event => {
  console.log('Service Worker: Message received', event.data);
  
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'GET_VERSION') {
    event.ports[0].postMessage({version: CACHE_NAME});
  }
});

console.log('Service Worker: Loaded successfully for Monit Amir Taxi Israel');
