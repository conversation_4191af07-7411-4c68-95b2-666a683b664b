<!DOCTYPE html>
<html lang="he" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>עמוד לא נמצא | Page Not Found | الصفحة غير موجودة - מונית אמיר Taxi Israel</title>
    <meta name="description" content="העמוד שחיפשת לא נמצא. חזור לעמוד הבית של מונית אמיר לשירותי הסעות מקצועיים בישראל | Page not found. Return to Monit Amir homepage for professional taxi services in Israel | الصفحة غير موجودة. العودة لموقع تاكسي أمير">
    <meta name="robots" content="noindex, follow">
    <link rel="canonical" href="https://monit-amir.web.app/">
    <meta name="keywords" content="404 error, page not found, taxi israel, מונית ישראל, تاكسي إسرائيل, monit amir">
    <meta name="geo.region" content="IL">
    <meta name="geo.placename" content="Israel">

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json">
    <meta name="theme-color" content="#FFD700">

    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
        }
        
        .error-container {
            max-width: 600px;
            padding: 2rem;
        }
        
        .error-code {
            font-size: 8rem;
            font-weight: bold;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .error-title {
            font-size: 2rem;
            margin-bottom: 1rem;
        }
        
        .error-message {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 30px;
            background: #FFD700;
            color: #333;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s ease;
            margin: 0 10px;
        }
        
        .btn:hover {
            background: #FFC107;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .taxi-icon {
            font-size: 4rem;
            margin-bottom: 2rem;
            animation: bounce 2s infinite;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="taxi-icon">
            <i class="fas fa-taxi"></i>
        </div>
        <div class="error-code">404</div>
        <h1 class="error-title">עמוד לא נמצא</h1>
        <p class="error-message">
            מצטערים, העמוד שחיפשת לא קיים או הועבר למקום אחר.<br>
            בואו נחזור לדרך הנכונה!
        </p>
        <div class="error-actions">
            <a href="/" class="btn">
                <i class="fas fa-home"></i>
                חזרה לעמוד הבית
            </a>
            <a href="/contact.html" class="btn">
                <i class="fas fa-phone"></i>
                צור קשר
            </a>
        </div>
    </div>

    <!-- Complete SEO Optimization Data -->
    <script type="application/ld+json" src="complete-seo-optimization.json"></script>

    <!-- Individual SEO Files -->
    <script type="application/ld+json" src="local-business.json"></script>
    <script type="application/ld+json" src="international-seo.json"></script>
    <script type="application/ld+json" src="google-my-business.json"></script>
</body>
</html>
