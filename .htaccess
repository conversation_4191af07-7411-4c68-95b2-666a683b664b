# SEO and Performance Optimization for מונית אמיר - Monit <PERSON>i Israel
# Optimized for maximum search engine visibility and performance

# Force HTTPS redirect for better SEO
RewriteEngine On
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Canonical URL enforcement
RewriteCond %{HTTP_HOST} !^monit-amir\.web\.app$ [NC]
RewriteRule ^(.*)$ https://monit-amir.web.app/$1 [L,R=301]

# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Browser Caching
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/ico "access plus 1 year"
    ExpiresByType image/icon "access plus 1 year"
    ExpiresByType text/html "access plus 1 day"
</IfModule>

# Security Headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# URL Redirects for SEO
RewriteEngine On

# Force HTTPS (if SSL is available)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Remove .html extension from URLs
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteRule ^([^\.]+)$ $1.html [NC,L]

# Redirect .html to clean URLs
RewriteCond %{THE_REQUEST} /([^.]+)\.html [NC]
RewriteRule ^ /%1 [NC,L,R=301]

# Custom Error Pages
ErrorDocument 404 /404.html
ErrorDocument 500 /500.html

# Prevent access to sensitive files
<Files "*.log">
    Order allow,deny
    Deny from all
</Files>

<Files ".htaccess">
    Order allow,deny
    Deny from all
</Files>

# Set default language and charset for SEO
DefaultLanguage he-IL
AddDefaultCharset UTF-8

# SEO-friendly URL rewriting
RewriteEngine On

# Remove .html extension for cleaner URLs
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteRule ^([^\.]+)$ $1.html [NC,L]

# Redirect .html to clean URLs
RewriteCond %{THE_REQUEST} /([^.]+)\.html [NC]
RewriteRule ^ /%1 [NC,L,R=301]

# Custom error pages
ErrorDocument 404 /404.html
ErrorDocument 500 /404.html

# Add language headers for multilingual SEO
<FilesMatch "\.(html|htm)$">
    Header set Content-Language "he, en, ar"
    Header set X-Content-Language "he-IL, en-US, ar-IL"
</FilesMatch>

# Add SEO headers
<FilesMatch "\.(html|htm)$">
    Header set X-Robots-Tag "index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1"
</FilesMatch>

# Sitemap and robots.txt optimization
<Files "sitemap.xml">
    Header set Content-Type "application/xml; charset=utf-8"
</Files>

<Files "robots.txt">
    Header set Content-Type "text/plain; charset=utf-8"
</Files>

# Schema.json optimization
<Files "schema.json">
    Header set Content-Type "application/ld+json; charset=utf-8"
    Header set Access-Control-Allow-Origin "*"
</Files>
